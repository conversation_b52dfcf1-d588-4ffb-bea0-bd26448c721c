# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/*.log
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# PM2 日志
.pm2/

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov/
coverage/
*.lcov
.nyc_output/

# 临时文件
.tmp/
.temp/

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 备份文件
*.bak
*.backup
*.old

# 测试文件
test-results/
coverage/

# 构建输出
dist/
build/
out/

# 缓存文件
.cache/
.parcel-cache/

# 其他
*.tgz
*.tar.gz
