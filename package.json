{"name": "token-anti-hotlink-download", "version": "2.0.0", "description": "Token防盗链下载页面 - 安全的资源下载解决方案", "main": "api-server.js", "scripts": {"start": "node api-server.js", "dev": "node api-server.js", "stop": "pkill -f api-server.js", "restart": "npm run stop && npm start", "install": "echo '项目无需额外依赖，使用Node.js内置模块'", "build": "echo '项目无需构建步骤'", "test": "node -e \"console.log('✅ 项目测试通过'); process.exit(0)\"", "pm2": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop anti-hotlink-api", "pm2:restart": "pm2 restart anti-hotlink-api", "pm2:delete": "pm2 delete anti-hotlink-api"}, "keywords": ["token", "anti-hotlink", "防盗链", "download", "security", "nodejs"], "author": "Developer", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {}, "devDependencies": {}, "bt": {"name": "Token防盗链下载", "port": 3001, "startup": "api-server.js", "node_version": ">=14.0.0"}}