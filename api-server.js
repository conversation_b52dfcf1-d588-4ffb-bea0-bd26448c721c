const http = require('http');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// 安全配置 - 这些敏感信息只存在于后端
const SECURE_CONFIG = {
    token: "126A126B",  // 防盗链密钥
    domain: "https://asset.vicfunxr.cn",
    filePath: "/xunifangzhenpingtai/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe",
    realFilePath: "/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe"
};

// 生成防盗链URL的安全函数
function generateSecureDownloadUrl() {
    // 获取当前时间戳并加30秒
    const now = new Date();
    const after30s = new Date(now.getTime() + 30 * 1000);
    const etime = Math.floor(after30s.getTime() / 1000);
    
    // 拼接字符串：token + etime + URI
    const concatString = SECURE_CONFIG.token + etime + SECURE_CONFIG.filePath;
    
    // 计算MD5签名
    const signature = crypto.createHash('md5').update(concatString, 'utf8').digest('hex');
    
    // 生成_fsst：从signature第9位开始取8位，然后拼接etime
    const fsstPart = signature.substring(8, 16);
    const fsst = fsstPart + etime;
    
    // 生成最终下载链接
    const downloadUrl = SECURE_CONFIG.domain + SECURE_CONFIG.realFilePath + "?_fsst=" + fsst;
    
    return {
        downloadUrl,
        expiresAt: after30s.toISOString(),
        signature: signature.substring(0, 8) // 只返回部分签名用于日志
    };
}

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json'
};

const server = http.createServer((req, res) => {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // API路由
    if (req.url === '/api/changelog' && req.method === 'GET') {
        // 读取更新日志文件
        const changelogPath = path.join(__dirname, '虚拟仿真课程资源云平台更新日志.txt');

        fs.readFile(changelogPath, 'utf8', (error, content) => {
            if (error) {
                console.error('读取更新日志失败:', error);
                res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
                res.end('读取更新日志失败');
            } else {
                res.writeHead(200, { 'Content-Type': 'text/plain; charset=utf-8' });
                res.end(content);
            }
        });

        return;
    }

    if (req.url === '/api/generate-download-url' && req.method === 'POST') {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const requestData = JSON.parse(body);
                
                // 简单的请求验证
                if (!requestData.fileName || !requestData.timestamp) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: false,
                        message: '请求参数不完整'
                    }));
                    return;
                }
                
                // 检查时间戳是否在合理范围内（防止重放攻击）
                const now = Date.now();
                const requestTime = parseInt(requestData.timestamp);
                if (Math.abs(now - requestTime) > 60000) { // 1分钟内的请求才有效
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: false,
                        message: '请求已过期'
                    }));
                    return;
                }
                
                // 生成安全的下载链接
                const result = generateSecureDownloadUrl();
                
                // 记录日志（不包含敏感信息）
                console.log(`生成下载链接: ${requestData.fileName}, 签名前缀: ${result.signature}, 过期时间: ${result.expiresAt}`);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    downloadUrl: result.downloadUrl,
                    expiresAt: result.expiresAt,
                    message: '下载链接生成成功'
                }));
                
            } catch (error) {
                console.error('API错误:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '服务器内部错误'
                }));
            }
        });
        
        return;
    }
    
    // 静态文件服务
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }
    
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';
    
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 - File Not Found</h1>', 'utf-8');
            } else {
                res.writeHead(500);
                res.end(`Server Error: ${error.code}`, 'utf-8');
            }
        } else {
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

const port = 3001;
server.listen(port, () => {
    console.log(`🔒 安全的防盗链服务器已启动！`);
    console.log(`📍 访问地址: http://localhost:${port}`);
    console.log(`🔑 API端点: http://localhost:${port}/api/generate-download-url`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log(`\n🛡️ 安全特性:`);
    console.log(`   - 敏感信息仅存储在后端`);
    console.log(`   - 防盗链算法在服务器端执行`);
    console.log(`   - 请求时间戳验证防止重放攻击`);
    console.log(`   - CORS支持跨域请求`);
    console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
