# 🎛️ 宝塔面板部署指南

## 📋 部署前准备

### 1. 宝塔面板要求
- 宝塔面板版本：7.0+
- 操作系统：Linux（推荐CentOS 7+/Ubuntu 18+）
- 内存：最少512MB

### 2. 安装Node.js管理器
1. 登录宝塔面板
2. 进入"软件商店"
3. 搜索"Node.js版本管理器"
4. 点击"安装"
5. 安装Node.js 14.0+版本

## 🚀 部署步骤

### 第一步：上传项目文件
1. 进入"文件管理"
2. 选择网站目录（如：`/www/wwwroot/your-domain.com`）
3. 上传项目文件或使用Git克隆

### 第二步：创建Node.js项目
1. 进入"Node.js项目管理"
2. 点击"添加Node.js项目"
3. 填写项目信息：
   - **项目名称**：Token防盗链下载
   - **项目目录**：选择上传的项目目录
   - **启动文件**：`api-server.js`
   - **端口**：`3001`
   - **Node版本**：选择已安装的Node.js版本

### 第三步：项目配置
1. 宝塔会自动识别`package.json`文件
2. 自动执行`npm install`（本项目无需额外依赖）
3. 自动配置PM2进程管理

### 第四步：启动项目
1. 在项目列表中找到刚创建的项目
2. 点击"启动"按钮
3. 项目状态变为"运行中"

### 第五步：配置反向代理（可选）
1. 进入"网站管理"
2. 添加站点或选择现有站点
3. 点击"反向代理"
4. 添加反向代理：
   - **代理名称**：api
   - **目标URL**：`http://127.0.0.1:3001`
   - **发送域名**：`$host`

## 🔧 配置说明

### 环境变量配置
如需使用环境变量，在项目管理中添加：
```
NODE_ENV=production
PORT=3001
ANTI_HOTLINK_TOKEN=your-token
RESOURCE_DOMAIN=https://your-domain.com
```

### 日志查看
- 在"Node.js项目管理"中点击"日志"
- 或查看`logs/`目录下的日志文件

### 性能监控
- 在项目列表中查看CPU、内存使用情况
- 点击"监控"查看详细性能数据

## 🌐 域名配置

### 1. 绑定域名
1. 在"网站管理"中添加站点
2. 绑定域名（如：`api.yourdomain.com`）
3. 配置反向代理到`http://127.0.0.1:3001`

### 2. SSL证书
1. 在站点设置中点击"SSL"
2. 选择"Let's Encrypt"免费证书
3. 或上传自有证书

### 3. 前端配置
修改`js/config.js`中的API端点：
```javascript
apiEndpoint: "https://api.yourdomain.com/api/generate-download-url"
```

## 🛡️ 安全配置

### 1. 防火墙设置
- 开放80端口（HTTP）
- 开放443端口（HTTPS）
- 3001端口仅内网访问

### 2. 访问限制
- 在"安全"中配置IP白名单
- 启用CC防护
- 配置请求频率限制

## 📊 监控和维护

### 1. 项目状态监控
- 在宝塔面板中实时查看项目状态
- 设置异常自动重启

### 2. 日志管理
- 定期清理日志文件
- 配置日志轮转

### 3. 备份策略
- 定期备份项目文件
- 备份数据库（如有）

## 🆘 常见问题

### Q: 项目启动失败
A: 检查Node.js版本是否>=14.0，查看错误日志

### Q: 端口被占用
A: 修改`package.json`中的端口配置，重新启动

### Q: 无法访问API
A: 检查防火墙设置和反向代理配置

### Q: 性能问题
A: 在宝塔面板中查看资源使用情况，考虑升级服务器配置

## 📞 技术支持

如遇到问题：
1. 查看宝塔面板的项目日志
2. 检查`logs/`目录下的详细日志
3. 确认配置文件是否正确
4. 验证网络连接和防火墙设置
