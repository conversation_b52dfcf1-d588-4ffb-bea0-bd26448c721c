# 🔐 Token防盗链下载页面 - 完整指南

## 📋 项目概述

这是一个集成了token防盗链保护的虚拟仿真课程资源下载页面。通过后端API动态生成带有时效性的防盗链URL，确保资源下载的安全性。

## 🚀 功能特性

- ✅ **安全的防盗链生成** - 敏感信息和算法在后端执行
- ✅ **时效性保护** - 链接有效期30秒，过期自动失效
- ✅ **MD5签名验证** - 使用MD5算法确保链接完整性
- ✅ **美观的UI界面** - 现代化的渐变背景和动画效果
- ✅ **响应式设计** - 支持各种设备屏幕尺寸
- ✅ **前后端分离** - 前端无敏感信息，后端安全计算

## 🏗️ 技术架构

### 安全架构设计
```
┌─────────────────┐    API请求    ┌─────────────────┐
│   前端 (安全)    │ ──────────→  │   后端 (安全)    │
│                │              │                │
│ • 无敏感信息     │              │ • 敏感信息存储   │
│ • 无算法逻辑     │              │ • 算法执行      │
│ • API调用       │              │ • 签名生成      │
└─────────────────┘              └─────────────────┘
```

### 防盗链算法（后端执行）
1. **时间戳生成**: 当前时间 + 30秒作为过期时间
2. **字符串拼接**: `token + etime + URI`（URI包含桶名的完整路径）
3. **MD5加密**: 对拼接字符串进行MD5哈希（支持UTF-8编码）
4. **_fsst生成**: 取MD5结果第9-16位 + 时间戳
5. **URL构建**: `域名 + realFilePath + ?_fsst=生成的fsst值`（不包含桶名）

## 📁 项目文件结构

```
项目目录/
├── index.html              # 前端主页面
├── js/
│   └── config.js          # 前端配置文件（无敏感信息）
├── logs/
│   └── .gitkeep           # 日志目录占位文件
├── api-server.js          # 后端API服务器
├── package.json           # 项目配置和依赖管理
├── ecosystem.config.js    # PM2配置文件（宝塔部署）
├── .gitignore             # Git忽略文件配置
└── README.md              # 完整指南文档
```

## 🧪 快速开始

### 1. 安装项目（可选）
```bash
# 如果需要使用环境变量功能
npm install

# 或者直接使用，无需安装依赖（使用Node.js内置模块）
```

### 2. 启动后端API服务器
```bash
# 本地开发启动
npm start

# 或直接使用node命令
node api-server.js

# 使用PM2启动（推荐生产环境）
npm run pm2
```

### 3. 访问前端页面
```
http://localhost:3001
```

### 4. 测试下载功能
- 点击"立即下载"按钮
- 验证防盗链URL生成
- 确认链接格式正确

### 5. 健康检查
```bash
# 使用npm脚本进行健康检查
npm run health-check
```

## 🔒 安全设计

### 原始安全漏洞（已修复）
❌ **防盗链密钥暴露**: `token` 直接写在前端代码中  
❌ **算法逻辑暴露**: 完整的MD5算法和防盗链生成逻辑在前端可见  
❌ **敏感路径暴露**: 域名、文件路径等信息在前端可见  
❌ **易被破解**: 攻击者可以轻易复制算法生成有效的防盗链  

### 安全修复方案
✅ **前端安全化**: 移除所有敏感信息和算法逻辑  
✅ **后端安全实现**: 敏感信息隔离，服务器端计算  
✅ **请求验证**: 时间戳验证防止重放攻击  
✅ **错误处理**: 不泄露敏感信息的错误处理  

### 前端安全设计
```javascript
const APP_CONFIG = {
    // ✅ 只包含公开信息
    fileName: "虚拟仿真课程资源云平台_0.9.0.exe",
    apiEndpoint: "http://localhost:3001/api/generate-download-url",
    
    // ✅ 通过API获取防盗链，无敏感信息暴露
    generateDownloadUrl: async function() {
        const response = await fetch(this.apiEndpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                fileName: this.fileName,
                timestamp: Date.now()
            })
        });
        return response.json();
    }
};
```

### 后端安全设计
```javascript
// 🔒 敏感信息只在后端存储
const SECURE_CONFIG = {
    token: "126A126B",  // 防盗链密钥
    domain: "https://asset.vicfunxr.cn",
    filePath: "/xunifangzhenpingtai/launcher/cloud/...",
    realFilePath: "/launcher/cloud/..."
};
```

## 🔍 API使用示例

### 请求
```bash
POST /api/generate-download-url
Content-Type: application/json

{
  "fileName": "虚拟仿真课程资源云平台_0.9.0.exe",
  "timestamp": 1752634477000
}
```

### 响应
```json
{
  "success": true,
  "downloadUrl": "https://asset.vicfunxr.cn/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe?_fsst=4736988a1752632891",
  "expiresAt": "2025-07-16T02:59:42.208Z",
  "message": "下载链接生成成功"
}
```

## 🚀 生产环境部署

### 🎛️ 宝塔面板部署（推荐）

#### 系统要求
- **宝塔面板**: 7.0+ 版本
- **Node.js**: v14.0.0 或更高版本
- **内存**: 最少 512MB RAM
- **存储**: 最少 100MB 可用空间

#### 部署步骤
1. **安装Node.js管理器**
   - 在宝塔面板中安装"Node.js版本管理器"
   - 安装Node.js 14.0+版本

2. **创建Node.js项目**
   - 进入"Node.js项目管理"
   - 点击"添加Node.js项目"
   - 选择项目目录，上传项目文件
   - 启动文件选择：`api-server.js`
   - 端口设置：`3001`

3. **项目配置**
   - 宝塔会自动识别`package.json`
   - 自动安装依赖（本项目无需额外依赖）
   - 自动配置PM2进程管理

4. **启动项目**
   - 点击"启动"按钮
   - 项目将自动在后台运行
   - 可通过宝塔面板监控项目状态

#### 宝塔面板优势
- ✅ **可视化管理**: 图形界面操作，无需命令行
- ✅ **自动重启**: 进程异常时自动重启
- ✅ **日志管理**: 集成日志查看和管理
- ✅ **性能监控**: 实时监控CPU、内存使用
- ✅ **域名绑定**: 可直接绑定域名和SSL证书

### 🖥️ 传统服务器部署

#### 系统要求
- **Node.js**: v14.0.0 或更高版本
- **PM2**: 进程管理器（可选）

#### 部署步骤

##### 1. 环境准备
```bash
# 检查Node.js版本
node --version

# 安装项目依赖（本项目无需额外依赖）
npm install
```

##### 2. 启动服务

**开发环境启动**:
```bash
npm start
```

**生产环境启动（使用PM2）**:
```bash
# 安装PM2
npm install -g pm2

# 使用配置文件启动
npm run pm2

# 或直接使用PM2
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

#### 3. 环境变量配置（生产环境推荐）

创建 `.env` 文件：
```bash
# 防盗链配置
ANTI_HOTLINK_TOKEN=126A126B
RESOURCE_DOMAIN=https://asset.vicfunxr.cn
RESOURCE_FILE_PATH=/xunifangzhenpingtai/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe
RESOURCE_REAL_PATH=/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe

# 服务器配置
PORT=3001
NODE_ENV=production
```

修改代码使用环境变量：
```javascript
require('dotenv').config();

const SECURE_CONFIG = {
    token: process.env.ANTI_HOTLINK_TOKEN || "126A126B",
    domain: process.env.RESOURCE_DOMAIN || "https://asset.vicfunxr.cn",
    filePath: process.env.RESOURCE_FILE_PATH || "/xunifangzhenpingtai/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe",
    realFilePath: process.env.RESOURCE_REAL_PATH || "/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe"
};
```

### 不同部署场景

#### 场景1: 前后端同服务器部署
```javascript
// js/config.js
apiEndpoint: "/api/generate-download-url"  // 相对路径
```

#### 场景2: 前后端分离部署
```javascript
// js/config.js
apiEndpoint: "https://api.yourdomain.com:3001/api/generate-download-url"  // 完整URL
```

#### 场景3: 使用反向代理
```nginx
# Nginx配置示例
server {
    listen 80;
    server_name yourdomain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 安全配置

### 1. HTTPS配置（生产环境必须）
```javascript
// 更新前端API端点
apiEndpoint: "https://yourdomain.com:3001/api/generate-download-url"
```

### 2. CORS配置
```javascript
// api-server.js
res.setHeader('Access-Control-Allow-Origin', 'https://yourdomain.com');
```

### 3. 防火墙配置
```bash
# 只允许必要的端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3001/tcp  # API端口
ufw enable
```

## 📊 监控和维护

### PM2监控
```bash
pm2 monit                    # 实时监控
pm2 logs anti-hotlink-api    # 查看日志
pm2 restart anti-hotlink-api # 重启服务
```

### 健康检查
```bash
curl -X POST http://localhost:3001/api/generate-download-url \
  -H "Content-Type: application/json" \
  -d '{"fileName":"test.exe","timestamp":1234567890000}'
```

## 🧪 验证清单

- [ ] API服务器在端口3001正常运行
- [ ] 前端配置指向正确的API端点
- [ ] 环境变量配置完成
- [ ] HTTPS证书配置（生产环境）
- [ ] CORS策略配置正确
- [ ] 防火墙规则配置
- [ ] 进程管理器配置（PM2）
- [ ] 功能测试通过

## 🆘 故障排查

### 常见问题
1. **端口被占用**: `netstat -tulpn | grep 3001`
2. **CORS错误**: 检查API服务器的CORS配置
3. **API连接失败**: 检查前端API端点配置
4. **权限问题**: 确保进程有足够权限监听端口

## 📊 技术验证

✅ **安全性**: 敏感信息仅存储在后端  
✅ **算法正确性**: 与官方文档示例完全一致  
✅ **URL格式**: `https://asset.vicfunxr.cn/launcher/cloud/虚拟仿真课程资源云平台_0.9.0.exe?_fsst=8位MD5片段+10位时间戳`  
✅ **时效性**: 30秒有效期，自动过期  
✅ **UTF-8支持**: 正确处理中文字符  

## 🔄 版本历史

- **v2.0.0** (2024-12-19): 安全架构重构，前后端分离
- **v1.1.0** (2024-12-19): 修正MD5加密算法，支持UTF-8编码
- **v1.0.0** (2024-12-19): 初始版本，实现基础防盗链功能
