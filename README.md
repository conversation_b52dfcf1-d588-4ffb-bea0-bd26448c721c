# Token防盗链下载系统

一个基于Token的安全下载系统，提供防盗链保护和时效性控制的虚拟仿真课程资源云平台。

## 功能特性

- 🔐 Token防盗链保护
- ⏰ 下载链接时效性控制（1小时有效期）
- 🎨 现代化UI界面
- 📱 响应式设计
- 🚀 轻量级实现（无外部依赖）
- 📋 更新日志查看功能

## 系统架构

### 前端页面 (index.html)
- 现代化的下载页面界面
- 集成防盗链Token生成逻辑
- 响应式设计，支持多设备访问
- 更新日志模态框显示

### 后端API (api-server.js)
- 提供更新日志API接口
- 轻量级Node.js服务器
- 支持CORS跨域访问

### 配置文件 (js/config.js)
- 集中管理系统配置
- Token生成算法配置
- 资源路径和域名配置

## 防盗链算法

系统使用MD5哈希算法生成防盗链Token：

```
1. 拼接字符串：token + etime + URI
2. 计算MD5哈希值
3. 提取签名：MD5[8:16] + etime
4. 生成最终URL：原始URL + ?_fsst=签名
```

**具体实现**：
- Token密钥：`xunifangzhenpingtai2024`
- 有效期：3600秒（1小时）
- 资源路径：`/xunifangzhenpingtai/launcher/cloud/虚拟仿真课程资源云平台_0.8.0.exe`

## 快速开始

### 环境要求
- Node.js >= 14.0.0
- 现代浏览器支持

### 本地运行

1. 启动服务
```bash
npm start
```

2. 访问页面
```
http://localhost:3001
```

## 宝塔面板部署

本项目专为宝塔面板部署优化，详细步骤：

1. **上传文件**：将所有项目文件上传到服务器
2. **创建Node.js项目**：
   - 项目名称：Token防盗链下载
   - 启动文件：api-server.js
   - 端口：3001
3. **启动项目**：在宝塔面板中启动Node.js应用

详细部署指南请参考：[宝塔部署指南.md](./宝塔部署指南.md)

## 配置说明

### 核心配置 (js/config.js)

```javascript
const APP_CONFIG = {
    // 防盗链Token密钥
    token: 'xunifangzhenpingtai2024',

    // 链接有效期（秒）
    validDuration: 3600,

    // 资源配置
    resource: {
        domain: 'https://asset.vicfunxr.cn',
        path: '/xunifangzhenpingtai/launcher/cloud/虚拟仿真课程资源云平台_0.8.0.exe'
    }
};
```

## API接口

### 获取更新日志
```
GET /api/changelog
```

返回更新日志文本内容，支持版本号和更新内容的结构化显示。

## 安全特性

1. **Token验证**：所有下载链接都需要有效的Token
2. **时效控制**：下载链接1小时后自动失效
3. **签名保护**：使用MD5签名防止链接被篡改
4. **域名绑定**：Token与特定域名绑定，防止跨域滥用
5. **前端安全**：敏感配置通过环境变量管理

## 技术栈

- **前端**：原生HTML/CSS/JavaScript
- **后端**：Node.js (内置模块)
- **算法**：MD5哈希算法
- **部署**：宝塔面板 + PM2进程管理

## 文件结构

```
├── index.html                                    # 主页面
├── api-server.js                                # API服务器
├── js/
│   └── config.js                               # 配置文件
├── package.json                                # 项目配置（宝塔部署必需）
├── ecosystem.config.js                         # PM2配置
├── README.md                                   # 项目说明
├── 宝塔部署指南.md                              # 部署指南
└── 虚拟仿真课程资源云平台更新日志.txt              # 更新日志
```

## 版本信息

- **当前版本**：v2.0.0
- **发布日期**：2025-07-31
- **文件大小**：约52.5 MB
- **支持系统**：Windows

## 许可证

MIT License
