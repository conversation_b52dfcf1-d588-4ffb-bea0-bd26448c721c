<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟仿真课程资源平台 - 立即获取</title>
    <script src="js/config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(-45deg, #667eea, #764ba2, #6B73FF, #000428);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 动态粒子背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 主容器 */
        .container {
            text-align: center;
            z-index: 10;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 60px 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            transform: translateY(0);
            animation: containerFloat 6s ease-in-out infinite;
            max-width: 600px;
            width: 90%;
        }

        @keyframes containerFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }

        .subtitle {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.6;
        }

        .download-btn {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            overflow: hidden;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.6);
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:active {
            transform: translateY(-1px) scale(1.02);
        }

        .download-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        /* 版本信息 */
        .version-info {
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        /* 特性列表 */
        .features {
            margin-top: 2.5rem;
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95rem;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            fill: #667eea;
        }

        /* 更新日志按钮 */
        .changelog-btn {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .changelog-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        .changelog-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* 更新日志模态框 */
        .changelog-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .changelog-modal.show {
            display: flex;
        }

        .changelog-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 0; /* 移除内边距，改为内部容器控制 */
            max-width: 600px;
            width: 100%;
            max-height: 80vh;
            overflow: hidden; /* 外层容器不滚动 */
            position: relative;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .changelog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0;
            padding: 20px 30px 15px 30px; /* 减少头部内边距 */
            border-bottom: 2px solid #667eea;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px 20px 0 0;
        }

        .changelog-body {
            color: #333;
            line-height: 1.6;
            max-height: calc(80vh - 70px); /* 减去头部高度，调整为更小的值 */
            overflow-y: auto; /* 只有内容区域滚动 */
            padding: 20px 30px 25px 30px; /* 顶部20px，左右30px，底部25px */
            margin-right: 10px; /* 为滚动条留出空间 */
        }

        /* 自定义滚动条样式 */
        .changelog-body::-webkit-scrollbar {
            width: 6px;
        }

        .changelog-body::-webkit-scrollbar-track {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            margin: 10px 5px; /* 上下和左右边距 */
        }

        .changelog-body::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .changelog-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
        }

        /* Firefox滚动条样式 */
        .changelog-body {
            scrollbar-width: thin;
            scrollbar-color: #667eea rgba(102, 126, 234, 0.1);
        }



        .changelog-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }



        .version-section {
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .version-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
        }

        .changelog-item {
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
        }

        .changelog-item::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 40px 30px;
                margin: 20px;
            }

            .title {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .features {
                flex-direction: column;
                gap: 1rem;
            }

            .changelog-btn {
                top: 20px;
                right: 20px;
                padding: 10px 16px;
                font-size: 0.8rem;
            }

            .changelog-content {
                padding: 20px;
                margin: 10px;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 2rem;
            }

            .download-btn {
                padding: 16px 32px;
                font-size: 1.1rem;
            }

            .changelog-btn {
                position: relative;
                top: auto;
                right: auto;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 更新日志按钮 -->
    <button class="changelog-btn" id="changelogBtn">
        <svg class="changelog-icon" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        更新日志
    </button>

    <!-- 更新日志模态框 -->
    <div class="changelog-modal" id="changelogModal">
        <div class="changelog-content">
            <div class="changelog-header">
                <h2 class="changelog-title">更新日志</h2>
                <button class="close-btn" id="closeBtn">&times;</button>
            </div>
            <div class="changelog-body" id="changelogBody">
                <!-- 更新日志内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 动态粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 主内容容器 -->
    <div class="container">
        <h1 class="title">虚拟仿真课程资源平台</h1>
        <p class="subtitle">助力新时代职业教育信息化<br>立即下载体验全新功能</p>
        
        <a href="#" class="download-btn" id="downloadBtn">
            <svg class="download-icon" viewBox="0 0 24 24">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            立即下载
        </a>
        
        <div class="version-info">
            版本 <span id="appVersion"></span> | 文件大小: <span id="appFileSize"></span> | 更新日期: <span id="appUpdateDate"></span>
        </div>
        
        <div class="features">
            <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24">
                    <path d="M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3z"/>
                </svg>
                快速安装
            </div>
            <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
                安全可靠
            </div>
            <!-- <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
                免费使用 -->
            </div>
        </div>
    </div>

    <script>
        // 创建动态粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置和动画延迟
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
                
                // 随机大小
                const size = Math.random() * 3 + 1;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                particlesContainer.appendChild(particle);
            }
        }

        // 页面加载完成后填充配置信息
        window.addEventListener('load', function() {
            createParticles();
            
            // 填充版本信息
            document.getElementById('appVersion').textContent = APP_CONFIG.version;
            document.getElementById('appFileSize').textContent = APP_CONFIG.fileSize;
            document.getElementById('appUpdateDate').textContent = APP_CONFIG.updateDate;
        });

        // 下载按钮点击事件
        document.getElementById('downloadBtn').addEventListener('click', async function(e) {
            e.preventDefault();

            // 显示加载状态
            const btn = this;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<svg class="download-icon" viewBox="0 0 24 24"><circle cx="12" cy="12" r="3"><animateTransform attributeName="transform" attributeType="XML" type="rotate" dur="1s" from="0 12 12" to="360 12 12" repeatCount="indefinite"/></circle></svg>生成中...';
            btn.disabled = true;

            try {
                // 生成带防盗链的下载URL
                const downloadUrl = await APP_CONFIG.generateDownloadUrl();

                // 跳转到下载链接
                window.location.href = downloadUrl;
            } catch (error) {
                console.error('下载失败:', error);
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        });

        // 更新日志相关功能
        const changelogBtn = document.getElementById('changelogBtn');
        const changelogModal = document.getElementById('changelogModal');
        const closeBtn = document.getElementById('closeBtn');
        const changelogBody = document.getElementById('changelogBody');

        // 显示更新日志
        changelogBtn.addEventListener('click', async function() {
            try {
                const response = await fetch('/api/changelog');
                const changelogText = await response.text();

                // 解析更新日志内容
                const formattedChangelog = parseChangelog(changelogText);
                changelogBody.innerHTML = formattedChangelog;

                // 显示模态框
                changelogModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            } catch (error) {
                console.error('加载更新日志失败:', error);
                changelogBody.innerHTML = '<p style="color: #e74c3c;">加载更新日志失败，请稍后重试。</p>';
                changelogModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });

        // 关闭模态框
        function closeModal() {
            changelogModal.classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        closeBtn.addEventListener('click', closeModal);

        // 点击模态框背景关闭
        changelogModal.addEventListener('click', function(e) {
            if (e.target === changelogModal) {
                closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && changelogModal.classList.contains('show')) {
                closeModal();
            }
        });

        // 解析更新日志文本
        function parseChangelog(text) {
            const lines = text.split('\n');
            let html = '';
            let currentVersion = '';
            let currentItems = [];

            for (let line of lines) {
                line = line.trim();

                if (line.startsWith('V') && line.match(/^V\d+\.\d+\.\d+/)) {
                    // 如果有之前的版本，先输出
                    if (currentVersion && currentItems.length > 0) {
                        html += createVersionSection(currentVersion, currentItems);
                    }

                    // 开始新版本
                    currentVersion = line;
                    currentItems = [];
                } else if (line.startsWith('-') && line.length > 1) {
                    // 更新项目
                    currentItems.push(line.substring(1).trim());
                }
            }

            // 输出最后一个版本
            if (currentVersion && currentItems.length > 0) {
                html += createVersionSection(currentVersion, currentItems);
            }

            return html || '<p>暂无更新日志</p>';
        }

        // 创建版本区块HTML
        function createVersionSection(version, items) {
            let html = `<div class="version-section">
                <div class="version-title">${version}</div>`;

            for (let item of items) {
                html += `<div class="changelog-item">${item}</div>`;
            }

            html += '</div>';
            return html;
        }

        // 添加鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const container = document.querySelector('.container');
            const x = (e.clientX / window.innerWidth) * 2 - 1;
            const y = (e.clientY / window.innerHeight) * 2 - 1;

            container.style.transform = `translateX(${x * 10}px) translateY(${y * 10}px)`;
        });
    </script>
</body>
</html>
