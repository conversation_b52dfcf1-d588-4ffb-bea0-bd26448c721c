// 安全的防盗链URL生成 - 通过后端API获取

// 应用配置信息
const APP_CONFIG = {
    // 版本信息
    version: "v0.9.0",
    fileSize: "92.2 MB",
    updateDate: "2025-07-30",

    // 公开配置（不包含敏感信息）
    fileName: "虚拟仿真课程资源云平台_0.9.0.exe",

    // API配置
    apiEndpoint: "https://hub.vicfunxr.cn/api/generate-download-url",  // 后端API地址

    // 应用名称
    appName: "虚拟仿真课程资源云平台",

    // 安全的防盗链URL生成函数 - 通过后端API
    generateDownloadUrl: async function() {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileName: this.fileName,
                    timestamp: Date.now()
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.downloadUrl) {
                return data.downloadUrl;
            } else {
                throw new Error(data.message || '生成下载链接失败');
            }
        } catch (error) {
            console.error('生成下载链接时出错:', error);
            // 降级处理：显示错误信息
            alert('获取下载链接失败，请稍后重试');
            throw error;
        }
    }
};

